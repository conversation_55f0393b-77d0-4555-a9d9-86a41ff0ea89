import React from 'react'
import { SignIn, SignUp } from '@clerk/clerk-react'
import { useState } from 'react'
import { Brain } from 'lucide-react'
import Button from '@/components/UI/Button'

const AuthPage: React.FC = () => {
  const [isSignUp, setIsSignUp] = useState(false)

  return (
    <>
      {/* 自定义CSS样式覆盖Clerk默认样式 */}
      <style>{`
        /* 强制覆盖Clerk组件样式 - 使用更高优先级 */
        .cl-formButtonPrimary,
        .cl-formButtonPrimary:not(:disabled),
        button[data-localization-key="formButtonPrimary"] {
          background: #059669 !important;
          background-color: #059669 !important;
          background-image: none !important;
          border-radius: 0 !important;
          border: none !important;
          font-weight: 400 !important;
          padding: 12px !important;
          color: white !important;
        }
        .cl-formButtonPrimary:hover,
        .cl-formButtonPrimary:not(:disabled):hover,
        button[data-localization-key="formButtonPrimary"]:hover {
          background: #047857 !important;
          background-color: #047857 !important;
          background-image: none !important;
        }
        .cl-formFieldInput {
          border-radius: 0 !important;
          background-color: #f8fafc !important;
          border: 1px solid #d6d3d1 !important;
          font-weight: 400 !important;
          padding: 12px !important;
        }
        .cl-formFieldInput:focus {
          border-color: #059669 !important;
          box-shadow: none !important;
          outline: none !important;
        }
        .cl-socialButtonsBlockButton {
          border-radius: 0 !important;
          background-color: #f5f5f4 !important;
          border: 1px solid #d6d3d1 !important;
          color: #1c1917 !important;
          font-weight: 400 !important;
          padding: 12px !important;
        }
        .cl-socialButtonsBlockButton:hover {
          background-color: #e7e5e4 !important;
        }
        .cl-footerActionLink {
          color: #059669 !important;
          text-decoration: none !important;
          font-weight: 400 !important;
        }
        .cl-footerActionLink:hover {
          color: #047857 !important;
        }
        .cl-dividerLine {
          background-color: #d6d3d1 !important;
        }
        .cl-dividerText {
          color: #78716c !important;
          font-weight: 400 !important;
        }
        .cl-formFieldLabel {
          color: #57534e !important;
          font-weight: 400 !important;
          margin-bottom: 8px !important;
        }

        /* 额外的按钮选择器确保覆盖 */
        [data-localization-key="formButtonPrimary"],
        button[type="submit"],
        .cl-button.cl-button--primary,
        .cl-formButtonPrimary * {
          background: #059669 !important;
          background-color: #059669 !important;
          background-image: none !important;
          border-radius: 0 !important;
          border: none !important;
          font-weight: 400 !important;
          color: white !important;
        }

        [data-localization-key="formButtonPrimary"]:hover,
        button[type="submit"]:hover,
        .cl-button.cl-button--primary:hover {
          background: #047857 !important;
          background-color: #047857 !important;
          background-image: none !important;
        }

        /* 强制Clerk组件居中 */
        .cl-rootBox,
        .cl-card {
          margin-left: 0 !important;
          margin-right: 0 !important;
          width: 100% !important;
        }

        .cl-main {
          width: 100% !important;
          margin: 0 !important;
        }

        /* 电话输入框样式覆盖 */
        .cl-phoneInputBox {
          border-radius: 0 !important;
          background-color: #f8fafc !important;
          border: 1px solid #d6d3d1 !important;
        }

        .cl-phoneInputBox:focus-within {
          border-color: #059669 !important;
          box-shadow: none !important;
        }

        /* 地区选择下拉菜单样式 */
        .cl-selectButton,
        .cl-selectButton__countryCode {
          background-color: #f8fafc !important;
          border: 1px solid #d6d3d1 !important;
          border-radius: 0 !important;
          color: #1c1917 !important;
          font-weight: 400 !important;
        }

        .cl-selectButton:hover {
          background-color: #e7e5e4 !important;
        }

        /* 下拉菜单选项样式 */
        .cl-selectOptionsContainer,
        .cl-selectOption {
          background-color: white !important;
          border: 1px solid #d6d3d1 !important;
          border-radius: 0 !important;
          color: #1c1917 !important;
        }

        .cl-selectOption:hover {
          background-color: #f5f5f4 !important;
          color: #1c1917 !important;
        }

        .cl-selectOption[data-selected="true"] {
          background-color: #059669 !important;
          color: white !important;
        }

        /* 修复下拉菜单的z-index和定位 */
        .cl-selectOptionsContainer {
          z-index: 9999 !important;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
        }

        /* 电话输入框内的输入字段 */
        .cl-phoneInputBox input {
          background-color: transparent !important;
          border: none !important;
          color: #1c1917 !important;
          font-weight: 400 !important;
        }

        .cl-phoneInputBox input:focus {
          outline: none !important;
          box-shadow: none !important;
        }
      `}</style>

      <div className="mobile-container min-h-screen bg-stone-50 dark:bg-stone-900 safe-top safe-bottom overflow-y-auto">
        <div className="flex flex-col min-h-screen">
          {/* Header with Integrated Features - Compact */}
          <div className="text-center pt-6 pb-4 px-4 flex-shrink-0">
            {/* Logo and Title */}
            <div className="flex items-center justify-center mb-2">
              <Brain className="w-8 h-8 text-stone-700 dark:text-stone-300 mr-2" />
              <h1 className="text-xl font-light text-stone-800 dark:text-stone-100 tracking-tight">
                Synapse
              </h1>
            </div>

            {/* Tagline */}
            <p className="text-stone-500 dark:text-stone-400 font-light text-sm mb-3">
              AI-Native Note App
            </p>

            {/* Compact Feature Pills */}
            <div className="flex flex-wrap justify-center gap-2 max-w-sm mx-auto">
              <div className="inline-flex items-center gap-1.5 px-2.5 py-1 bg-emerald-50 dark:bg-emerald-900/20 text-emerald-700 dark:text-emerald-400 text-xs font-light border border-emerald-200 dark:border-emerald-800">
                <div className="w-1 h-1 bg-emerald-600 rounded-full"></div>
                AI Organization
              </div>
              <div className="inline-flex items-center gap-1.5 px-2.5 py-1 bg-amber-50 dark:bg-amber-900/20 text-amber-700 dark:text-amber-400 text-xs font-light border border-amber-200 dark:border-amber-800">
                <div className="w-1 h-1 bg-amber-600 rounded-full"></div>
                Multi-Modal
              </div>
              <div className="inline-flex items-center gap-1.5 px-2.5 py-1 bg-stone-100 dark:bg-stone-800 text-stone-600 dark:text-stone-300 text-xs font-light border border-stone-200 dark:border-stone-700">
                <div className="w-1 h-1 bg-stone-400 rounded-full"></div>
                Universal Sync
              </div>
            </div>
          </div>

          {/* Auth Section - 移除白色框，让Clerk组件自由布局 */}
          <div className="flex-1 px-4 pb-4">
            {/* Toggle Buttons */}
            <div className="flex bg-stone-100 dark:bg-stone-700 p-1 mb-4 max-w-sm mx-auto">
              <Button
                variant={!isSignUp ? 'primary' : 'ghost'}
                size="sm"
                onClick={() => setIsSignUp(false)}
                className="flex-1 font-normal text-xs"
              >
                Sign In
              </Button>
              <Button
                variant={isSignUp ? 'primary' : 'ghost'}
                size="sm"
                onClick={() => setIsSignUp(true)}
                className="flex-1 font-normal text-xs"
              >
                Sign Up
              </Button>
            </div>

            {/* Clerk Auth Components - 强制完全居中 */}
            <div className="w-full flex justify-center px-4">
              <div className="w-full max-w-sm">
                {isSignUp ? (
              <SignUp
                appearance={{
                  variables: {
                    colorPrimary: '#059669', // emerald-600
                    colorBackground: 'transparent',
                    colorInputBackground: '#f8fafc', // stone-50
                    colorInputText: '#1c1917', // stone-800
                    borderRadius: '0px', // 矩形设计，无圆角
                    fontFamily: 'Inter, system-ui, sans-serif',
                    fontSize: '0.875rem',
                    spacingUnit: '0.75rem'
                  },
                  elements: {
                    rootBox: "w-full !mx-0",
                    card: "shadow-none border-none bg-transparent w-full",
                    headerTitle: "hidden",
                    headerSubtitle: "hidden",
                    socialButtonsBlockButton: "!bg-stone-100 dark:!bg-stone-700 !border !border-stone-200 dark:!border-stone-600 !text-stone-800 dark:!text-stone-200 hover:!bg-stone-200 dark:hover:!bg-stone-600 !font-normal !text-sm !py-3 !rounded-none",
                    formButtonPrimary: "!bg-emerald-700 hover:!bg-emerald-800 !font-normal !text-sm !py-3 !rounded-none !border-none",
                    footerActionLink: "!text-emerald-700 hover:!text-emerald-800 !font-normal !text-sm !no-underline",
                    formFieldInput: "!bg-stone-50 dark:!bg-stone-800 !border !border-stone-200 dark:!border-stone-700 !text-stone-800 dark:!text-stone-200 !font-normal !text-sm !py-3 !rounded-none focus:!border-emerald-600 focus:!ring-0 focus:!outline-none",
                    formFieldLabel: "!text-stone-600 dark:!text-stone-400 !font-normal !text-sm !mb-2",
                    dividerLine: "!bg-stone-200 dark:!bg-stone-700",
                    dividerText: "!text-stone-500 dark:!text-stone-400 !font-normal !text-sm"
                  }
                }}
                redirectUrl="/"
              />
            ) : (
              <SignIn
                appearance={{
                  variables: {
                    colorPrimary: '#059669', // emerald-600
                    colorBackground: 'transparent',
                    colorInputBackground: '#f8fafc', // stone-50
                    colorInputText: '#1c1917', // stone-800
                    borderRadius: '0px', // 矩形设计，无圆角
                    fontFamily: 'Inter, system-ui, sans-serif',
                    fontSize: '0.875rem',
                    spacingUnit: '0.75rem'
                  },
                  elements: {
                    rootBox: "w-full !mx-0",
                    card: "shadow-none border-none bg-transparent w-full",
                    headerTitle: "hidden",
                    headerSubtitle: "hidden",
                    socialButtonsBlockButton: "!bg-stone-100 dark:!bg-stone-700 !border !border-stone-200 dark:!border-stone-600 !text-stone-800 dark:!text-stone-200 hover:!bg-stone-200 dark:hover:!bg-stone-600 !font-normal !text-sm !py-3 !rounded-none",
                    formButtonPrimary: "!bg-emerald-700 hover:!bg-emerald-800 !font-normal !text-sm !py-3 !rounded-none !border-none",
                    footerActionLink: "!text-emerald-700 hover:!text-emerald-800 !font-normal !text-sm !no-underline",
                    formFieldInput: "!bg-stone-50 dark:!bg-stone-800 !border !border-stone-200 dark:!border-stone-700 !text-stone-800 dark:!text-stone-200 !font-normal !text-sm !py-3 !rounded-none focus:!border-emerald-600 focus:!ring-0 focus:!outline-none",
                    formFieldLabel: "!text-stone-600 dark:!text-stone-400 !font-normal !text-sm !mb-2",
                    dividerLine: "!bg-stone-200 dark:!bg-stone-700",
                    dividerText: "!text-stone-500 dark:!text-stone-400 !font-normal !text-sm"
                  }
                }}
                redirectUrl="/"
              />
                )}
              </div>
            </div>

            {/* Footer - Compact */}
            <div className="text-center py-4 px-4 flex-shrink-0">
              <p className="text-xs text-stone-400 dark:text-stone-500 font-normal leading-relaxed">
                By continuing, you agree to our Terms of Service and Privacy Policy
              </p>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default AuthPage
