import React, { useState, useEffect, useRef } from 'react'
import { Moon, Sun, Search, X, RefreshCw, Command, LogOut } from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '@clerk/clerk-react'
import { useAppStore } from '@/store/appStore'
import { useAPI } from '@/services/api'
import useGlobalSearch from '@/hooks/useGlobalSearch'
import { useSearchNavigation } from '@/services/searchNavigation'
import { DEV_CONFIG } from '@/config/development'
import { env } from '@/config/environment'
import Button from '../UI/Button'
import Input from '../UI/Input'
import SearchResults from '../Search/SearchResults'
import ConfirmDialog from '../UI/ConfirmDialog'
import { cn } from '@/lib/utils'

const Header: React.FC = () => {
  const { theme, toggleTheme, lastRefreshTime, setUser, setAuthenticated } = useAppStore()
  const { refreshEntries, isRefreshing } = useAPI()
  const { searchState, search, clearSearch, addToRecentSearches } = useGlobalSearch()
  const { navigateToResult, setSearchContext } = useSearchNavigation()
  const navigate = useNavigate()

  // Only use Clerk hooks when authentication is enabled
  const authData = env.clerk.enableAuth ? useAuth() : null
  const { signOut } = authData || { signOut: null }

  const [showSearch, setShowSearch] = useState(false)
  const [showResults, setShowResults] = useState(false)
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false)
  const [isLoggingOut, setIsLoggingOut] = useState(false)
  const searchInputRef = useRef<HTMLInputElement>(null)
  const searchContainerRef = useRef<HTMLDivElement>(null)

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Cmd+K or Ctrl+K to open search
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault()
        handleSearchToggle()
      }

      // Escape to close search
      if (e.key === 'Escape' && showSearch) {
        handleSearchClose()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [showSearch])

  // Click outside to close search results
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (searchContainerRef.current && !searchContainerRef.current.contains(e.target as Node)) {
        setShowResults(false)
      }
    }

    if (showResults) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showResults])

  const handleSearchToggle = () => {
    if (showSearch) {
      handleSearchClose()
    } else {
      setShowSearch(true)
      setShowResults(false)
      setTimeout(() => searchInputRef.current?.focus(), 100)
    }
  }

  const handleSearchClose = () => {
    setShowSearch(false)
    setShowResults(false)
    clearSearch()
  }

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value
    search(query)
    setShowResults(query.trim().length > 0)
  }

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchState.query.trim() && searchState.results.total > 0) {
      // Add to recent searches
      addToRecentSearches(searchState.query)

      // If there are results, navigate to the first one
      const firstResult =
        searchState.results.notes[0] ||
        searchState.results.todos[0] ||
        searchState.results.calendar[0] ||
        searchState.results.inbox[0]

      if (firstResult) {
        handleResultClick(firstResult)
      }
    }
  }

  const handleResultClick = (result: any) => {
    // Store search context for navigation
    setSearchContext({
      itemId: result.id,
      type: result.type,
      action: 'highlight'
    })

    // Navigate to the appropriate page
    const path = navigateToResult(result)
    navigate(path)

    // Close search
    handleSearchClose()

    // Add to recent searches
    addToRecentSearches(searchState.query)
  }

  const handleShowMore = (type: any) => {
    // Navigate to the specific page with search context
    const path = type === 'notes' ? '/notes' :
                 type === 'todos' ? '/todos' :
                 type === 'calendar' ? '/calendar' : '/inbox'

    setSearchContext({
      itemId: '',
      type,
      action: 'search',
    })

    navigate(path)
    handleSearchClose()
  }

  const handleRefresh = async () => {
    try {
      console.log('🔄 Manual refresh triggered by user')
      await refreshEntries()
      console.log('✅ Manual refresh completed successfully')
    } catch (error) {
      console.error('❌ Manual refresh failed:', error)
      // Error is already handled in the API hook
    }
  }

  const formatLastRefreshTime = () => {
    if (!lastRefreshTime) return 'Never'
    const now = Date.now()
    const diff = now - lastRefreshTime
    const minutes = Math.floor(diff / 60000)
    const seconds = Math.floor((diff % 60000) / 1000)

    if (minutes > 0) {
      return `${minutes}m ago`
    } else if (seconds > 0) {
      return `${seconds}s ago`
    } else {
      return 'Just now'
    }
  }

  const handleLogoutClick = () => {
    setShowLogoutConfirm(true)
  }

  const handleLogoutConfirm = async () => {
    try {
      setIsLoggingOut(true)
      console.log('🚪 Logging out user...')

      // Clear local state first
      setUser(null)
      setAuthenticated(false)

      // If using Clerk authentication, sign out
      if (env.clerk.enableAuth && signOut) {
        await signOut()
        console.log('✅ Clerk sign out completed')
      } else if (DEV_CONFIG.SKIP_AUTH) {
        // In development mode, just clear local storage
        localStorage.clear()
        sessionStorage.clear()
        console.log('✅ Development mode: Local storage cleared')
      }

      // Navigate to auth page
      navigate('/auth')
      console.log('✅ User logged out successfully')
    } catch (error) {
      console.error('❌ Logout failed:', error)
      // Even if Clerk logout fails, clear local state and redirect
      setUser(null)
      setAuthenticated(false)
      navigate('/auth')
    } finally {
      setIsLoggingOut(false)
      setShowLogoutConfirm(false)
    }
  }

  const handleLogoutCancel = () => {
    setShowLogoutConfirm(false)
  }

  return (
    <header className="sticky top-0 z-50 backdrop-blur-glass border-b border-stone-200 dark:border-stone-700 bg-white/95 dark:bg-stone-800/95">
      <div className="px-5 py-4">
        {!showSearch ? (
          <div className="flex items-center justify-between">
            {/* Brand - Scandinavian Clean */}
            <div className="flex items-center gap-2">
              <h1 className="text-xl font-light text-stone-800 dark:text-stone-100">
                Synapse
              </h1>
              <span className="text-xs text-stone-500 dark:text-stone-400 font-normal">
                AI-Native Note App
              </span>
            </div>

            {/* Actions */}
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRefresh}
                className="p-2 text-stone-600 dark:text-stone-400 hover:text-stone-800 dark:hover:text-stone-200 hover:bg-stone-100 dark:hover:bg-stone-700"
                title={`Refresh Data (Last: ${formatLastRefreshTime()})`}
                disabled={isRefreshing}
              >
                <RefreshCw className={`w-5 h-5 ${isRefreshing ? 'animate-spin' : ''}`} />
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={toggleTheme}
                className="p-2 text-stone-600 dark:text-stone-400 hover:text-stone-800 dark:hover:text-stone-200 hover:bg-stone-100 dark:hover:bg-stone-700"
                title="Toggle Theme"
              >
                {theme === 'light' ? (
                  <Moon className="w-5 h-5" />
                ) : (
                  <Sun className="w-5 h-5" />
                )}
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={handleSearchToggle}
                className="p-2 relative text-stone-600 dark:text-stone-400 hover:text-stone-800 dark:hover:text-stone-200 hover:bg-stone-100 dark:hover:bg-stone-700 transition-all duration-200 hover:scale-105 active:scale-95"
                title="Search (⌘K)"
              >
                <Search className="w-5 h-5 transition-transform duration-200" />
                <span className="absolute -top-1 -right-1 text-xs text-stone-400 dark:text-stone-500">
                  <Command className="w-3 h-3" />
                </span>
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={handleLogoutClick}
                className="p-2 text-stone-600 dark:text-stone-400 hover:text-red-600 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-all duration-200"
                title="Logout"
              >
                <LogOut className="w-5 h-5" />
              </Button>
            </div>
          </div>
        ) : (
          <div ref={searchContainerRef} className="relative animate-in slide-in-from-top-2 fade-in duration-200">
            <form onSubmit={handleSearchSubmit} className="flex items-center gap-2">
              <div className="relative flex-1">
                <Input
                  ref={searchInputRef}
                  value={searchState.query}
                  onChange={handleSearchChange}
                  placeholder="Search across all content..."
                  className="flex-1 pr-10"
                  autoFocus
                />
                {searchState.isSearching && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <div className="animate-spin w-4 h-4 border-2 border-accent-primary border-t-transparent rounded-full" />
                  </div>
                )}
              </div>

              <Button
                type="submit"
                variant="primary"
                size="sm"
                disabled={!searchState.query.trim() || searchState.results.total === 0}
                className="px-4"
              >
                Search
              </Button>

              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={handleSearchClose}
                className="p-2"
                title="Close (Esc)"
              >
                <X className="w-5 h-5" />
              </Button>
            </form>

            {/* Search Results Dropdown */}
            {showResults && (
              <div className={cn(
                'absolute top-full left-0 right-0 mt-2 bg-white dark:bg-stone-800 border border-stone-200 dark:border-stone-700 shadow-lg',
                'max-h-96 overflow-hidden z-50',
                'animate-in slide-in-from-top-2 fade-in duration-200 ease-out'
              )}>
                <SearchResults
                  results={searchState.results}
                  isSearching={searchState.isSearching}
                  hasSearched={searchState.hasSearched}
                  query={searchState.query}
                  onResultClick={handleResultClick}
                  onShowMore={handleShowMore}
                />
              </div>
            )}
          </div>
        )}
      </div>

      {/* Logout Confirmation Dialog */}
      <ConfirmDialog
        isOpen={showLogoutConfirm}
        onClose={handleLogoutCancel}
        onConfirm={handleLogoutConfirm}
        title="Confirm Logout"
        message="Are you sure you want to logout? You will need to sign in again to access your data."
        confirmText="Logout"
        cancelText="Cancel"
        variant="warning"
        isLoading={isLoggingOut}
      />
    </header>
  )
}

export default Header
